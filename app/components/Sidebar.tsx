import { Link, useLocation, useParams } from "react-router";
import { useState, useMemo } from "react";
import { MessageSquare, Bell, Plus, User as UserIcon } from "lucide-react";

import type { MyGroup, UserProfile } from "~/lib/api/types";
import { useNotifications } from "~/lib/api/client-queries";
import { useAppContext } from "~/lib/providers/app-context";

interface SidebarProps {
  isOpen?: boolean;
  groups?: MyGroup[];
  profile?: UserProfile | null;
}

export function Sidebar({
  isOpen = true,
  groups = [],
  profile = null,
}: SidebarProps) {
  const location = useLocation();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Fetch notifications to get unread count
  const { data: notificationsData } = useNotifications();
  const { streamNotificationCounts } = useAppContext();
  console.log("Notifications", notificationsData);

  // Calculate unread count from legacy API notifications (memoized for performance)
  const legacyUnreadCount = useMemo(() => {
    if (!notificationsData?.pages) return 0;

    return notificationsData.pages.reduce((total, page) => {
      if (!page.success) return total;

      const unreadInPage = page.data.notifications.filter(
        (notification) => !notification.isRead
      ).length;

      return total + unreadInPage;
    }, 0);
  }, [notificationsData]);

  // Combine legacy and GetStream notification counts
  const totalUnreadCount = legacyUnreadCount + streamNotificationCounts.unread;

  console.log("Sidebar notification counts:", {
    legacy: legacyUnreadCount,
    stream: streamNotificationCounts.unread,
    total: totalUnreadCount,
    streamNotificationCounts,
  });

  // Format the badge display (show "20+" if 20 or more)
  const badgeDisplay =
    totalUnreadCount >= 20
      ? "20+"
      : totalUnreadCount > 0
      ? totalUnreadCount.toString()
      : null;

  const navItems = [
    {
      id: "chat",
      label: "Chat",
      path: "/chat",
      icon: <MessageSquare className="w-6 h-6" />,
    },
    {
      id: "notifications",
      label: "Notifications",
      path: "/notifications",
      icon: <Bell className="w-6 h-6" />,
      badge: badgeDisplay,
    },
  ];

  return (
    <aside
      className={`bg-zinc-900 h-screen flex flex-col transition-all duration-300 overflow-hidden ${
        !isOpen ? "w-0" : "w-auto"
      }`}
    >
      {/* Top Groups Section */}
      <div className="p-3 space-y-3">
        {groups.slice(0, 3).map((group) => (
          <Link
            key={group.id}
            to={`/groups/${group.externalId}`}
            className="block w-12 h-12 rounded-xl overflow-hidden bg-zinc-700 cursor-pointer hover:ring-2 hover:ring-indigo-500 transition-all"
          >
            {group.bannerImage ? (
              <img
                src={group.bannerImage}
                alt={group.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                <span className="text-white text-xs font-bold">
                  {group.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </Link>
        ))}

        {/* Add New Button */}
        <Link to="/explore">
          <button className="w-12 h-12 rounded-xl bg-zinc-800 hover:bg-zinc-700 flex items-center justify-center transition-colors cursor-pointer">
            <Plus className="w-5 h-5 text-zinc-400" />
          </button>
        </Link>
      </div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Navigation Items */}
      <nav className="p-3 space-y-3">
        {navItems.map((item) => {
          const isActive = location.pathname === item.path;
          const isHovered = hoveredItem === item.id;

          return (
            <Link
              key={item.id}
              to={item.path}
              className="relative block"
              onMouseEnter={() => setHoveredItem(item.id)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <div
                className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all ${
                  isActive
                    ? "bg-indigo-600 text-white"
                    : "bg-zinc-800 text-zinc-400 hover:bg-zinc-700 hover:text-white"
                }`}
              >
                {item.icon}
                {item.badge && (
                  <div className="absolute -top-1 -right-1 bg-red-500 text-white text-[0.55rem] rounded-full w-5 h-5 flex items-center justify-center">
                    {item.badge}
                  </div>
                )}
              </div>

              {/* Tooltip */}
              {isHovered && (
                <div className="absolute left-20 top-1/2 transform -translate-y-1/2 bg-zinc-900 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap z-10">
                  {item.label}
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-zinc-900 rotate-45" />
                </div>
              )}
            </Link>
          );
        })}

        {/* User Profile */}
        <Link
          to="/profile"
          className="relative block"
          onMouseEnter={() => setHoveredItem("profile")}
          onMouseLeave={() => setHoveredItem(null)}
        >
          <div
            className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all overflow-hidden ${
              location.pathname === "/profile"
                ? "ring-2 ring-indigo-600"
                : "hover:ring-2 hover:ring-zinc-600"
            }`}
          >
            {profile?.avatarUrl ? (
              <img
                src={profile.avatarUrl}
                alt={`${profile.firstName} ${profile.lastName}`}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-zinc-600 to-zinc-700 flex items-center justify-center">
                {profile ? (
                  <span className="text-white text-lg font-semibold">
                    {profile.firstName?.charAt(0).toUpperCase() ||
                      profile.email?.charAt(0).toUpperCase() ||
                      "U"}
                  </span>
                ) : (
                  <UserIcon className="w-6 h-6 text-zinc-300" />
                )}
              </div>
            )}
          </div>

          {/* Tooltip */}
          {hoveredItem === "profile" && (
            <div className="absolute left-20 top-1/2 transform -translate-y-1/2 bg-zinc-900 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap z-10">
              Profile
              <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-zinc-900 rotate-45" />
            </div>
          )}
        </Link>
      </nav>
    </aside>
  );
}
