import { useEffect, useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router";
import { type EnrichedActivity, type EnrichedReaction } from "getstream";
import {
  Loader2,
  MessageSquare,
  Heart,
  Share2,
  MoreHorizontal,
  Send,
  ArrowLeft,
  X,
  Link as <PERSON>I<PERSON>,
  Check,
} from "lucide-react";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import type { Route } from "./+types/[activityId]";
import { useAppContext } from "~/lib/providers/app-context";
import { useProfile, useMyCohortModule } from "~/lib/api/client-queries";

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: EnrichedReaction[];
    comment?: EnrichedReaction[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

type CommentWithUser = EnrichedReaction & {
  user?: {
    id: string;
    created_at: string;
    updated_at: string;
    data: {
      name?: string;
      image?: string;
    };
  };
};

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Post - Sphere` },
    { name: "description", content: "Individual post view" },
  ];
}

export default function PostDetailPage() {
  const { groupId, cohortId, moduleId, activityId } = useParams();
  const { userId, streamClient } = useAppContext();
  const { data: profileData } = useProfile();

  // Get module data to access feed configuration
  const {
    data: moduleResponse,
    isLoading: moduleLoading,
    isError: moduleError,
  } = useMyCohortModule(groupId!, cohortId!, moduleId!);

  const [activity, setActivity] = useState<EnrichedActivityWithText | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [comments, setComments] = useState<CommentWithUser[]>([]);
  const [commentInput, setCommentInput] = useState("");
  const [loadingComments, setLoadingComments] = useState(false);
  const [shareDropdownOpen, setShareDropdownOpen] = useState(false);
  const [showToast, setShowToast] = useState(false);

  // Initialize dayjs with plugins
  dayjs.extend(relativeTime);
  dayjs.extend(utc);
  dayjs.extend(timezone);

  // Fetch individual activity
  useEffect(() => {
    const fetchActivity = async () => {
      if (!streamClient || !moduleResponse?.data || moduleLoading) return;

      try {
        setLoading(true);
        setError(null);

        const module = moduleResponse.data;

        // Get the feed using the config from the module
        const feed = streamClient.feed(
          module.config.feedGroup,
          module.config.feedId
        );

        // Fetch the specific activity
        const response = await feed.get({
          limit: 1,
          id_lte: activityId,
          id_gte: activityId,
          withReactionCounts: true,
          withOwnReactions: true,
          enrich: true,
        });

        if (response.results.length === 0) {
          setError("Post not found");
          return;
        }

        const activityData = response.results[0] as EnrichedActivityWithText;
        setActivity(activityData);

        // Fetch comments for this activity
        setLoadingComments(true);
        try {
          const commentsResponse = await streamClient.reactions.filter({
            activity_id: activityId!,
            kind: "comment",
            limit: 50,
          });

          setComments(commentsResponse.results as CommentWithUser[]);
        } catch (commentsError) {
          console.error("Error fetching comments:", commentsError);
        } finally {
          setLoadingComments(false);
        }
      } catch (err) {
        console.error("Error fetching activity:", err);
        setError("Failed to load post. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchActivity();
  }, [streamClient, moduleResponse, moduleLoading, activityId]);

  // Close share dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShareDropdownOpen(false);
    };

    if (shareDropdownOpen) {
      document.addEventListener("click", handleClickOutside);
      return () => {
        document.removeEventListener("click", handleClickOutside);
      };
    }
  }, [shareDropdownOpen]);

  const handleLike = async () => {
    if (!streamClient || !activity) return;

    try {
      const reaction = await streamClient.reactions.add("like", activity.id);

      setActivity({
        ...activity,
        own_reactions: {
          ...activity.own_reactions,
          like: [...(activity.own_reactions?.like || []), reaction],
        },
        reaction_counts: {
          ...activity.reaction_counts,
          like: (activity.reaction_counts?.like || 0) + 1,
        },
      });
    } catch (error) {
      console.error("Error adding like:", error);
    }
  };

  const handleUnlike = async (reactionId: string) => {
    if (!streamClient || !activity) return;

    try {
      await streamClient.reactions.delete(reactionId);

      setActivity({
        ...activity,
        own_reactions: {
          ...activity.own_reactions,
          like: activity.own_reactions?.like?.filter(
            (r) => r.id !== reactionId
          ),
        },
        reaction_counts: {
          ...activity.reaction_counts,
          like: Math.max((activity.reaction_counts?.like || 0) - 1, 0),
        },
      });
    } catch (error) {
      console.error("Error removing like:", error);
    }
  };

  const handleAddComment = async () => {
    if (!streamClient || !commentInput.trim() || !activity) return;

    try {
      const comment = await streamClient.reactions.add("comment", activity.id, {
        text: commentInput,
      });

      // Add comment to local state with proper user data structure
      const newComment: CommentWithUser = {
        ...comment,
        user: {
          id: userId!,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          data: {
            name:
              profileData?.data?.firstName && profileData?.data?.lastName
                ? `${profileData.data.firstName} ${profileData.data.lastName}`.trim()
                : profileData?.data?.firstName ||
                  profileData?.data?.lastName ||
                  "Anonymous",
          },
        },
      };

      setComments([newComment, ...comments]);

      // Update activity comment count
      setActivity({
        ...activity,
        reaction_counts: {
          ...activity.reaction_counts,
          comment: (activity.reaction_counts?.comment || 0) + 1,
        },
      });

      // Clear input
      setCommentInput("");
    } catch (error) {
      console.error("Error adding comment:", error);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = dayjs.utc(timestamp).local();
    const now = dayjs();
    const diffInDays = now.diff(date, "day");

    if (diffInDays > 7) {
      return date.format("MMM D, YYYY");
    }

    return date.fromNow();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") return actor;
    if (actor?.data?.name) return actor.data.name;
    if (actor?.id) return actor.id;
    return "Unknown User";
  };

  const getActorInitial = (actor: any): string => {
    const name = getActorName(actor);
    return name[0]?.toUpperCase() || "U";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    if (typeof activity.object === "string") return activity.object;
    const obj = activity.object as any;
    if (obj?.text) return obj.text;
    if (obj?.content) return obj.content;
    if (obj?.id) return `${activity.verb} ${obj.id}`;
    return `${activity.verb}`;
  };

  const handleShare = async () => {
    const postUrl = window.location.href;

    try {
      await navigator.clipboard.writeText(postUrl);
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);
      console.log("Link copied to clipboard!");
    } catch (err) {
      // Fallback for older browsers
      try {
        const textArea = document.createElement("textarea");
        textArea.value = postUrl;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
        console.log("Link copied to clipboard (fallback)!");
      } catch (fallbackErr) {
        console.error("Failed to copy link:", fallbackErr);
      }
    }
  };

  if (moduleLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (moduleError || error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black">
        <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-300 mb-4">
          {error || "Failed to load post"}
        </div>
        <Link
          to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Feed
        </Link>
      </div>
    );
  }

  if (!activity) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black">
        <p className="text-zinc-400 mb-4">Post not found</p>
        <Link
          to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Feed
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-black">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-900">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center gap-4">
            <Link
              to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
              className="text-zinc-400 hover:text-white flex items-center gap-2 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              Back to Feed
            </Link>
          </div>
        </div>
      </div>

      {/* Post Content */}
      <div className="flex-1 overflow-auto bg-black">
        <div className="max-w-4xl mx-auto px-8 py-12">
          <div className="bg-zinc-900 rounded-lg shadow-sm border border-zinc-700 p-6">
          {/* Activity Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              {getActorImage(activity.actor) ? (
                <img
                  src={getActorImage(activity.actor)!}
                  alt={getActorName(activity.actor)}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold">
                    {getActorInitial(activity.actor)}
                  </span>
                </div>
              )}
              <div>
                <p className="font-medium text-white">
                  {getActorName(activity.actor)}
                </p>
                <p className="text-sm text-zinc-400">
                  {formatTime(activity.time)}
                </p>
              </div>
            </div>
            <button className="text-zinc-400 hover:text-white transition-colors">
              <MoreHorizontal className="w-5 h-5" />
            </button>
          </div>

          {/* Activity Content */}
          <div className="mb-4">
            <p className="text-zinc-200">{getActivityContent(activity)}</p>

            {/* Display legacy image field */}
            {activity.image && (
              <img
                src={activity.image}
                alt="Activity image"
                className="mt-3 rounded-lg max-w-full"
              />
            )}

            {/* Display attachments */}
            {activity.attachments && activity.attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                {activity.attachments.map((attachment, index) => (
                  <div key={index}>
                    {attachment.type === "image" && attachment.image_url && (
                      <img
                        src={attachment.image_url}
                        alt="Attached image"
                        className="rounded-lg max-w-full"
                      />
                    )}
                    {attachment.type === "file" && attachment.asset_url && (
                      <a
                        href={attachment.asset_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-3 py-2 bg-zinc-100 rounded-lg hover:bg-zinc-200 transition-colors"
                      >
                        <span className="text-sm text-zinc-700">
                          📎 View File
                        </span>
                      </a>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Activity Actions */}
          <div className="flex items-center gap-6 pt-4 border-t border-zinc-700">
            {activity.own_reactions?.like?.length ? (
              <button
                onClick={() =>
                  handleUnlike(activity.own_reactions!.like![0].id)
                }
                className="flex items-center gap-2 text-blue-400 hover:text-blue-300 transition-colors"
              >
                <Heart className="w-5 h-5 fill-current" />
                <span className="text-sm">
                  {activity.reaction_counts?.like || 0}
                </span>
              </button>
            ) : (
              <button
                onClick={handleLike}
                className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
              >
                <Heart className="w-5 h-5" />
                <span className="text-sm">
                  {activity.reaction_counts?.like || 0}
                </span>
              </button>
            )}
            <div className="flex items-center gap-2 text-zinc-400">
              <MessageSquare className="w-5 h-5" />
              <span className="text-sm">
                {activity.reaction_counts?.comment || 0}
              </span>
            </div>
            <div className="relative">
              <button
                onClick={() => setShareDropdownOpen(!shareDropdownOpen)}
                className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
              >
                <Share2 className="w-5 h-5" />
                <span className="text-sm">Share</span>
              </button>

              {shareDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg py-1 min-w-[140px] z-10">
                  <button
                    onClick={() => {
                      handleShare();
                      setShareDropdownOpen(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm text-zinc-200 hover:bg-zinc-700 transition-colors flex items-center gap-2"
                  >
                    <LinkIcon className="w-4 h-4" />
                    Copy Link
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

          {/* Comments Section */}
          <div className="mt-4 pt-4">
            <h3 className="text-lg font-semibold text-white mb-4">Comments</h3>

            {/* Comment Input */}
            <div className="flex gap-2 mb-6">
              <input
                type="text"
                value={commentInput}
                onChange={(e) => setCommentInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleAddComment();
                  }
                }}
                placeholder="Write a comment..."
                className="flex-1 px-3 py-2 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-zinc-400"
              />
              <button
                onClick={handleAddComment}
                disabled={!commentInput.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>

            {/* Comments List */}
            {loadingComments ? (
              <div className="flex justify-center py-4">
                <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
              </div>
            ) : (
              <div className="space-y-4">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex gap-3">
                    {comment.user?.data?.image ? (
                      <img
                        src={comment.user.data.image}
                        alt={comment.user.data.name || "User"}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
                        <span className="text-zinc-300 text-sm font-medium">
                          {(comment.user?.data?.name || "U")[0].toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="bg-zinc-700 rounded-lg px-3 py-2">
                        <p className="text-sm font-medium text-white">
                          {comment.user?.data?.name || "Anonymous"}
                        </p>
                        <p className="text-sm text-zinc-300">
                          {(comment.data as any)?.text || comment.data}
                        </p>
                      </div>
                      <p className="text-xs text-zinc-500 mt-1">
                        {formatTime(comment.created_at)}
                      </p>
                    </div>
                  </div>
                ))}
                {comments.length === 0 && (
                  <p className="text-sm text-zinc-400 text-center py-4">
                    No comments yet. Be the first to comment!
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 bg-zinc-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <Check className="w-4 h-4" />
          Link copied to clipboard!
        </div>
      )}
    </div>
  );
}
