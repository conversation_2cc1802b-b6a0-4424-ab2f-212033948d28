import { useState, useEffect, useCallback } from "react";
import { useAppContext } from "~/lib/providers/app-context";
import type { StreamNotificationGroup } from "./types";

interface NotificationCounts {
  unread: number;
  unseen: number;
  total: number;
}

/**
 * Hook to get notification counts from GetStream
 * Returns unread, unseen, and total notification counts
 */
export function useNotificationCounts() {
  const { streamClient, userId } = useAppContext();
  const [counts, setCounts] = useState<NotificationCounts>({
    unread: 0,
    unseen: 0,
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(false);

  // Function to manually update counts (for immediate UI updates)
  // Supports both relative updates (negative/positive numbers) and absolute updates
  const updateCounts = useCallback(
    (
      updates: Partial<NotificationCounts> & {
        setUnseenToZero?: boolean;
        decrementUnread?: boolean;
      }
    ) => {
      setCounts((prev) => ({
        unread: updates.decrementUnread
          ? Math.max(0, prev.unread - 1)
          : updates.unread !== undefined
          ? Math.max(0, prev.unread + updates.unread)
          : prev.unread,
        unseen: updates.setUnseenToZero
          ? 0
          : updates.unseen !== undefined
          ? Math.max(0, prev.unseen + updates.unseen)
          : prev.unseen,
        total:
          updates.total !== undefined
            ? Math.max(0, prev.total + updates.total)
            : prev.total,
      }));
    },
    []
  );

  const fetchCounts = useCallback(async () => {
    if (!streamClient || !userId) {
      setCounts({ unread: 0, unseen: 0, total: 0 });
      return;
    }

    setIsLoading(true);
    try {
      const notificationFeed = streamClient.feed("notification", userId);
      const response = await notificationFeed.get({
        limit: 100, // Get more notifications to get accurate counts
        withReactionCounts: false, // Don't need reaction counts for counting
        withOwnReactions: false, // Don't need own reactions for counting
      });

      const groups = response.results as StreamNotificationGroup[];

      const unreadCount = groups.filter((group) => !group.is_read).length;
      const unseenCount = groups.filter((group) => !group.is_seen).length;
      const totalCount = groups.length;

      setCounts({
        unread: unreadCount,
        unseen: unseenCount,
        total: totalCount,
      });
    } catch (error) {
      console.error("Error fetching notification counts:", error);
      setCounts({ unread: 0, unseen: 0, total: 0 });
    } finally {
      setIsLoading(false);
    }
  }, [streamClient, userId]);

  useEffect(() => {
    fetchCounts();
  }, [fetchCounts]);

  return {
    counts,
    isLoading,
    refetch: fetchCounts,
    updateCounts,
  };
}

/**
 * Hook to mark notifications as read or seen
 */
export function useNotificationActions() {
  const { streamClient, userId } = useAppContext();

  const markAsRead = useCallback(
    async (notificationIds: string[]) => {
      if (!streamClient || !userId) {
        throw new Error("Stream client or userId not available");
      }

      const notificationFeed = streamClient.feed("notification", userId);
      await notificationFeed.get({
        limit: 1,
        mark_read: notificationIds,
      });
    },
    [streamClient, userId]
  );

  const markAsSeen = useCallback(async () => {
    if (!streamClient || !userId) {
      throw new Error("Stream client or userId not available");
    }

    const notificationFeed = streamClient.feed("notification", userId);
    await notificationFeed.get({
      limit: 1,
      mark_seen: true,
    });
  }, [streamClient, userId]);

  return {
    markAsRead,
    markAsSeen,
  };
}
