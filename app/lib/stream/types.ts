// GetStream notification types
export interface StreamActor {
  id: string;
  data: {
    name?: string;
    image?: string;
  };
  created_at: string;
  updated_at: string;
}

export interface StreamAttachment {
  type: string;
  image_url?: string;
  custom?: {
    fileName?: string;
  };
}

export interface StreamReaction {
  id: string;
  kind: string;
  user_id: string;
  user: {
    id: string;
    data: {
      name?: string;
    };
  };
  activity_id: string;
  data: {
    text?: string;
  };
  created_at: string;
}

export interface StreamActivity {
  id: string;
  time: string;
  verb: string;
  actor: StreamActor | string;
  object: string;
  origin: string;
  message?: string;
  attachments?: StreamAttachment[];
  own_reactions?: {
    [key: string]: StreamReaction[];
  };
  reaction_counts?: {
    [key: string]: number;
  };
  foreign_id?: string;
  target?: string;
}

export interface StreamNotificationGroup {
  id: string;
  verb: string;
  is_read: boolean;
  is_seen: boolean;
  created_at: string;
  updated_at: string;
  activity_count: number;
  actor_count: number;
  activities: StreamActivity[];
  group: string;
}

// UI notification types
export interface ActorSummary {
  count: number;
  names: string[];
  avatars: string[];
}

export interface PostSummary {
  id: string;
  message?: string;
  images?: string[];
  reactionCounts?: {
    like?: number;
    comment?: number;
  };
}

export interface UIBaseNotification {
  id: string;
  createdAt: string;
  isRead: boolean;
  verb: "post" | "like" | "comment" | "follow" | "arrived" | "generic";
}

export interface PostNotification extends UIBaseNotification {
  verb: "post";
  actors: ActorSummary;
  post: PostSummary;
  activityCount: number;
}

export interface LikeNotification extends UIBaseNotification {
  verb: "like";
  actors: ActorSummary;
  targetPost: PostSummary;
}

export interface CommentNotification extends UIBaseNotification {
  verb: "comment";
  actors: ActorSummary;
  targetPost: PostSummary;
  commentPreview?: string;
}

export interface FollowNotification extends UIBaseNotification {
  verb: "follow";
  actors: ActorSummary;
}

export interface ArrivedNotification extends UIBaseNotification {
  verb: "arrived";
  actors: ActorSummary;
  message: string;
}

export interface GenericNotification extends UIBaseNotification {
  verb: "generic";
  actors: ActorSummary;
  message: string;
}

export type UINotification =
  | PostNotification
  | LikeNotification
  | CommentNotification
  | FollowNotification
  | ArrivedNotification
  | GenericNotification;
